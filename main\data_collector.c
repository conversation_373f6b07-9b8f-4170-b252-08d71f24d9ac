#include "data_collector.h"
#include "rolling_buffer.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "i2c_ms583730b.h"
#include "adc_ntc.h"

static const char *TAG = "COLLECTOR";

// 全局状态变量
static data_collector_state_t collector_state = DATA_COLLECTOR_STOPPED;
static uint8_t current_frequency = 10;
static ms5837_t sensor;

// 任务相关变量
static TaskHandle_t sampling_task_handle = NULL;
#define SAMPLING_TASK_STACK_SIZE 4096
#define SAMPLING_TASK_PRIORITY 4
#define SAMPLING_TASK_NAME "sampling_task"

// 使用ADC采样电路测温
// 返回温度值 x 10 (例：26.3°C 返回 263)
esp_err_t get_temperature(int16_t *temp)
{
    esp_err_t ret;
    float temp_float = 0;
    ret = adc_ntc_read_chan0(&temp_float);
    if (ret == ESP_OK)
    {
        *temp = (int16_t)(temp_float * 10); // 转换为整型 x 10
    }
    else
    {
        ESP_LOGW(TAG, "读取温度失败: %s", esp_err_to_name(ret));
        *temp = 0;
    }
    return ret;
}

// 使用MS5837取压力(I2C通讯)
// 返回压力值 x 10 (例：101.3kPa 返回 1013)
esp_err_t get_pressure(uint16_t *press)
{
    esp_err_t ret;
    float temp_float = 0, press_float = 0;
    ret = ms5837_get_data(&sensor, &temp_float, &press_float);
    if (ret == ESP_OK)
    {
        *press = (uint16_t)(press_float * 10); // 转换为整型 x 10
    }
    else
    {
        ESP_LOGE(TAG, "MS5837 读取失败 错误: %s", esp_err_to_name(ret));
        *press = 0;
    }
    return ret;
}

// 数据采集任务
static void sampling_task(void *pvParameters)
{
    esp_err_t ret;

    // 初始化 MS5837 传感器
    // ret = ms5837_init(&sensor);
    // if (ret != ESP_OK)
    // {
    //     ESP_LOGE(TAG, "Failed to initialize MS5837 sensor: %s", esp_err_to_name(ret));
    // }

    // 初始化ADC
    ret = adc_ntc_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize ADC");
    }

    // 添加初始延迟，让传感器完全稳定
    vTaskDelay(pdMS_TO_TICKS(1500)); // 等待1.5秒

    while (1)
    {
        // 检查采集器状态，如果停止则退出循环
        if (collector_state == DATA_COLLECTOR_STOPPED)
        {
            break;
        }

        // 采集传感器数据
        sensor_data_t sensor_data;
        int16_t temperature = 0;
        uint16_t pressure = 0;

        // 读取温度数据（使用ADC）
        esp_err_t temp_ret = get_temperature(&temperature);

        // 读取压力数据（使用MS5837）
        // esp_err_t press_ret = get_pressure(&pressure);

        // // 如果至少有一个传感器读取成功，就将数据添加到缓冲区
        if (temp_ret == ESP_OK)
        {
            sensor_data.temperature = temperature;

            // 将数据添加到滚动缓冲区
            rb_append(&sensor_data);

            ESP_LOGI(TAG, "数据采集成功 - 温度: %.1f°C, 压力: %.1f kPa", temperature / 10.0, pressure / 10.0);
        }
        else
        {
            ESP_LOGE(TAG, "传感器数据读取失败 - 温度: %s", esp_err_to_name(temp_ret));
        }

        // 根据当前频率计算延迟时间（毫秒）
        uint32_t delay_ms = 1000 / current_frequency;
        vTaskDelay(pdMS_TO_TICKS(delay_ms));
    }
}

esp_err_t data_collector_init(uint8_t frequency)
{
    // 验证频率范围
    if (frequency < 1 || frequency > 20)
    {
        ESP_LOGE(TAG, "无效的采集频率: %d (有效范围: 1-20)", frequency);
        return ESP_ERR_INVALID_ARG;
    }

    // 初始化滚动缓冲区
    rb_init();

    // 设置采集频率
    current_frequency = frequency;
    collector_state = DATA_COLLECTOR_RUNNING;

    // 创建数据采集任务
    BaseType_t task_ret = xTaskCreate(
        sampling_task,            // 任务函数
        SAMPLING_TASK_NAME,       // 任务名称
        SAMPLING_TASK_STACK_SIZE, // 栈大小
        NULL,                     // 任务参数
        SAMPLING_TASK_PRIORITY,   // 任务优先级
        &sampling_task_handle     // 任务句柄
    );

    if (task_ret != pdPASS)
    {
        ESP_LOGE(TAG, "创建数据采集任务失败");
        collector_state = DATA_COLLECTOR_STOPPED;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "数据采集器初始化完成，采集频率: %d Hz", frequency);
    return ESP_OK;
}

esp_err_t data_collector_deinit(void)
{
    // 停止采集
    data_collector_stop();

    // 等待任务自然退出
    if (sampling_task_handle != NULL)
    {
        // 给任务一些时间来检查状态并退出
        vTaskDelay(pdMS_TO_TICKS(100));

        // 如果任务还在运行，强制删除
        vTaskDelete(sampling_task_handle);
        sampling_task_handle = NULL;
        ESP_LOGI(TAG, "数据采集任务已删除");
    }

    // 清空缓冲区
    rb_clear();

    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    return ESP_OK;
}

esp_err_t data_collector_set_frequency(uint8_t frequency)
{
    if (frequency < 1 || frequency > 20)
    {
        ESP_LOGE(TAG, "无效的采集频率: %d (有效范围: 1-20)", frequency);
        return ESP_ERR_INVALID_ARG;
    }

    // 检查采集器是否已初始化
    if (sampling_task_handle == NULL)
    {
        ESP_LOGE(TAG, "数据采集器未初始化，请先调用 data_collector_init()");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "修改采集频率: %d Hz -> %d Hz", current_frequency, frequency);

    // 直接修改频率变量，任务会在下次循环时使用新频率
    current_frequency = frequency;

    ESP_LOGI(TAG, "采集频率修改成功: %d Hz", frequency);
    return ESP_OK;
}

esp_err_t data_collector_stop(void)
{
    if (collector_state == DATA_COLLECTOR_STOPPED)
    {
        return ESP_OK; // 已经停止
    }

    ESP_LOGI(TAG, "停止数据采集");

    // 设置状态为停止，任务会检查这个状态并退出
    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    ESP_LOGI(TAG, "数据采集已停止");
    return ESP_OK;
}

data_collector_state_t data_collector_get_state(void)
{
    return collector_state;
}

uint8_t data_collector_get_frequency(void)
{
    return current_frequency;
}

int data_collector_get_latest_data(sensor_data_t *output, int n)
{
    return rb_get_latest_n(output, n);
}

void data_collector_clear_buffer(void)
{
    rb_clear();
}
